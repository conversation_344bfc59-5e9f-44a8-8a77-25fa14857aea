/**
 * 通用AI提供商接口定义
 */

export interface AIProviderConfig {
  apiKey: string;
  baseUrl: string;
  modelName: string;
  timeout?: number;
}

export interface AIImageRecognitionResult {
  foods: Array<{
    name: string;
    weight: number;
    calories: number;
    confidence: number;
    dataSource: 'nutrition_label' | 'visual_estimation';
    nutrition: {
      protein: number;
      fat: number;
      carbs: number;
      fiber: number;
      sugar: number;
      sodium: number;
    };
    labelInfo: {
      hasLabel: boolean;
      confidence: number;
      readableText: string;
    };
    portionAnalysis: {
      estimatedPortion: string;
      referenceObject: string;
      confidenceLevel: 'high' | 'medium' | 'low';
    };
  }>;
  analysisMetadata: {
    hasNutritionLabel: boolean;
    imageQuality: 'high' | 'medium' | 'low';
    recognitionMethod: string;
    processingNotes: string;
  };
  multiImageAnalysis?: {
    totalImages: number;
    duplicatesFound: number;
    crossReferenceNotes: string;
  };
}

export interface AITextAnalysisResult {
  foods: Array<{
    name: string;
    weight: number;
    calories: number;
    confidence: number;
    nutrition: {
      protein: number;
      fat: number;
      carbs: number;
      fiber: number;
      sugar: number;
      sodium: number;
    };
  }>;
  analysisMetadata: {
    recognitionMethod: string;
    processingNotes: string;
  };
}

export interface AIValidationResult {
  isValid: boolean;
  error?: string;
  modelList?: string[];
}

/**
 * 通用AI提供商接口
 */
export interface AIProvider {
  /**
   * 提供商名称
   */
  readonly name: string;

  /**
   * 初始化提供商
   */
  initialize(config: AIProviderConfig): void;

  /**
   * 图片识别
   */
  recognizeFood(files: File[], additionalContext?: string, userContext?: any): Promise<AIImageRecognitionResult>;

  /**
   * 文本分析
   */
  analyzeText(text: string): Promise<AITextAnalysisResult>;

  /**
   * 营养建议分析
   */
  analyzeNutritionAdvice(prompt: string): Promise<{ advice: string; rawResponse?: any }>;

  /**
   * 验证配置
   */
  validateConfig(config: AIProviderConfig): Promise<AIValidationResult>;

  /**
   * 获取可用模型列表
   */
  getAvailableModels(config: AIProviderConfig): Promise<string[]>;
}

// {{ AURA-X: Add - 创建通用AI提供商接口. Approval: 寸止(ID:**********). }}
