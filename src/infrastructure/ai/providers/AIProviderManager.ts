/**
 * AI提供商管理器
 */

import { AIProvider, AIProviderConfig } from './AIProvider';
import { GeminiProvider } from './GeminiProvider';
import { OpenAIProvider } from './OpenAIProvider';
import { AIModelProvider } from '../../../shared/types/aiModel';
// {{ AURA-X: Add - 导入OpenAI提供商. Approval: 寸止(ID:**********). }}

export class AIProviderManager {
  private providers: Map<AIModelProvider, AIProvider> = new Map();
  private currentProvider: AIProvider | null = null;

  constructor() {
    this.initializeProviders();
  }

  /**
   * 初始化所有提供商
   */
  private initializeProviders(): void {
    // 注册Gemini提供商
    this.providers.set('gemini', new GeminiProvider());

    // 注册OpenAI提供商
    this.providers.set('openai', new OpenAIProvider());
    // {{ AURA-X: Modify - 注册OpenAI提供商. Approval: 寸止(ID:**********). }}
  }

  /**
   * 设置当前提供商
   */
  setProvider(providerType: AIModelProvider, config: AIProviderConfig): void {
    const provider = this.providers.get(providerType);
    if (!provider) {
      throw new Error(`不支持的AI提供商: ${providerType}`);
    }

    provider.initialize(config);
    this.currentProvider = provider;
  }

  /**
   * 获取当前提供商
   */
  getCurrentProvider(): AIProvider | null {
    return this.currentProvider;
  }

  /**
   * 获取指定提供商
   */
  getProvider(providerType: AIModelProvider): AIProvider | null {
    return this.providers.get(providerType) || null;
  }

  /**
   * 获取所有支持的提供商类型
   */
  getSupportedProviders(): AIModelProvider[] {
    return Array.from(this.providers.keys());
  }

  /**
   * 验证提供商配置
   */
  async validateProviderConfig(providerType: AIModelProvider, config: AIProviderConfig): Promise<{ isValid: boolean; error?: string; modelList?: string[] }> {
    const provider = this.providers.get(providerType);
    if (!provider) {
      return {
        isValid: false,
        error: `不支持的AI提供商: ${providerType}`
      };
    }

    return await provider.validateConfig(config);
  }

  /**
   * 获取提供商的可用模型列表
   */
  async getProviderModels(providerType: AIModelProvider, config: AIProviderConfig): Promise<string[]> {
    const provider = this.providers.get(providerType);
    if (!provider) {
      throw new Error(`不支持的AI提供商: ${providerType}`);
    }

    return await provider.getAvailableModels(config);
  }
}

// 创建全局实例
export const aiProviderManager = new AIProviderManager();

// {{ AURA-X: Add - 创建AI提供商管理器. Approval: 寸止(ID:**********). }}
