/**
 * OpenAI AI提供商实现
 */

import { AIProvider, AIProviderConfig, AIImageRecognitionResult, AITextAnalysisResult, AIValidationResult } from './AIProvider';
import { promptManager } from '../prompts/promptManager';

// {{ AURA-X: Remove - 移除重试机制，改为单次执行. Approval: 寸止(ID:**********). }}

export class OpenAIProvider implements AIProvider {
  public readonly name = 'openai';

  private apiKey: string = '';
  private baseUrl: string = '';
  private originalBaseUrl: string = ''; // {{ AURA-X: Add - 保存原始baseUrl用于判断#结尾. Approval: 寸止(ID:**********). }}
  private modelName: string = '';
  private timeout: number = 60000; // {{ AURA-X: Modify - 增加超时时间到60秒. Approval: 寸止(ID:**********). }}

  /**
   * 初始化提供商
   */
  initialize(config: AIProviderConfig): void {
    this.apiKey = config.apiKey;
    this.originalBaseUrl = config.baseUrl; // {{ AURA-X: Add - 保存原始baseUrl. Approval: 寸止(ID:**********). }}
    this.baseUrl = this.normalizeBaseUrl(config.baseUrl);
    this.modelName = config.modelName;
    this.timeout = config.timeout || 30000;
  }

  /**
   * 标准化Base URL，处理尾部斜杠和特殊标记
   */
  private normalizeBaseUrl(baseUrl: string): string {
    // {{ AURA-X: Modify - 处理#结尾的特殊情况和尾部斜杠. Approval: 寸止(ID:**********). }}
    console.log('🔍 [OpenAIProvider.normalizeBaseUrl] 输入URL:', baseUrl);

    if (baseUrl.endsWith('#')) {
      console.log('🔧 [OpenAIProvider.normalizeBaseUrl] 检测到#结尾URL');
      // 如果以#结尾，移除#并提取基础路径
      const urlWithoutHash = baseUrl.slice(0, -1);
      console.log('🔧 [OpenAIProvider.normalizeBaseUrl] 移除#后:', urlWithoutHash);
      // 提取基础路径，例如从 https://open.bigmodel.cn/api/paas/v4/chat/completions
      // 提取到 https://open.bigmodel.cn/api/paas/v4
      const pathParts = urlWithoutHash.split('/');
      console.log('🔧 [OpenAIProvider.normalizeBaseUrl] 路径部分:', pathParts);
      // 移除最后一个路径部分（如 completions）
      if (pathParts.length > 3 && pathParts[pathParts.length - 1] !== '') {
        const removedPart = pathParts.pop();
        console.log('🔧 [OpenAIProvider.normalizeBaseUrl] 移除部分:', removedPart);
      }
      const result = pathParts.join('/');
      console.log('🔧 [OpenAIProvider.normalizeBaseUrl] 最终结果:', result);
      return result;
    }
    const result = baseUrl.replace(/\/+$/, ''); // 移除尾部的一个或多个斜杠
    console.log('🔧 [OpenAIProvider.normalizeBaseUrl] 普通URL处理结果:', result);
    return result;
  }

  /**
   * 构建API URL，处理#结尾的特殊情况
   */
  private buildApiUrl(endpoint: string): string {
    // {{ AURA-X: Modify - 修复#结尾URL的API路径构建. Approval: 寸止(ID:**********). }}
    console.log('🔍 [OpenAIProvider.buildApiUrl] 构建URL:', {
      endpoint: endpoint,
      originalBaseUrl: this.originalBaseUrl,
      normalizedBaseUrl: this.baseUrl,
      endsWithHash: this.originalBaseUrl.endsWith('#')
    });

    // 如果原始URL以#结尾，使用标准化后的baseUrl直接拼接endpoint
    if (this.originalBaseUrl.endsWith('#')) {
      const result = `${this.baseUrl}${endpoint}`;
      console.log('🚀 [OpenAIProvider.buildApiUrl] #结尾URL构建结果:', result);
      return result;
    }
    // 正常情况下添加v1路径
    const result = `${this.baseUrl}/v1${endpoint}`;
    console.log('🚀 [OpenAIProvider.buildApiUrl] 普通URL构建结果:', result);
    return result;
  }

  /**
   * 图片识别
   */
  async recognizeFood(files: File[], additionalContext?: string, _userContext?: any): Promise<AIImageRecognitionResult> {
    if (!this.apiKey) {
      throw new Error('OpenAI API密钥未配置');
    }

    try {
      // 将所有图片转换为base64
      const base64Images = await Promise.all(
        files.map(file => this.fileToBase64(file))
      );

      // {{ AURA-X: Modify - 移除重试机制，直接调用. Approval: 寸止(ID:**********). }}
      const response = await this.sendImageRequest(base64Images, additionalContext, _userContext);

      // 解析响应
      return this.parseImageResponse(response);
    } catch (error) {
      console.error('OpenAI食物识别失败:', error);
      throw new Error(error instanceof Error ? error.message : 'OpenAI食物识别失败');
    }
  }

  /**
   * 文本分析
   */
  async analyzeText(text: string): Promise<AITextAnalysisResult> {
    if (!this.apiKey) {
      throw new Error('OpenAI API密钥未配置');
    }

    try {
      // {{ AURA-X: Modify - 移除重试机制，直接调用. Approval: 寸止(ID:**********). }}
      const response = await this.sendTextRequest(text);
      return this.parseTextResponse(response);
    } catch (error) {
      console.error('OpenAI文本分析失败:', error);
      throw new Error(error instanceof Error ? error.message : 'OpenAI文本分析失败');
    }
  }

  /**
   * 营养建议分析
   */
  async analyzeNutritionAdvice(prompt: string): Promise<{ advice: string; rawResponse?: any }> {
    if (!this.apiKey) {
      throw new Error('OpenAI API密钥未配置');
    }

    try {
      const response = await this.sendAdviceRequest(prompt);
      return this.parseAdviceResponse(response);
    } catch (error) {
      console.error('OpenAI营养建议分析失败:', error);
      throw new Error(error instanceof Error ? error.message : 'OpenAI营养建议分析失败');
    }
  }

  /**
   * 验证配置
   */
  async validateConfig(config: AIProviderConfig): Promise<AIValidationResult> {
    try {
      const normalizedBaseUrl = this.normalizeBaseUrl(config.baseUrl);
      // {{ AURA-X: Modify - 修复#结尾URL的模型获取路径. Approval: 寸止(ID:**********). }}
      const apiUrl = config.baseUrl.endsWith('#')
        ? `${normalizedBaseUrl}/models`
        : `${normalizedBaseUrl}/v1/models`;
      const response = await fetch(apiUrl, {
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
      }

      const result = await response.json();
      
      if (!result.data || !Array.isArray(result.data)) {
        throw new Error('API响应格式不正确');
      }

      return {
        isValid: true,
        modelList: result.data.map((model: any) => model.id)
      };
    } catch (error) {
      return {
        isValid: false,
        error: error instanceof Error ? error.message : '验证失败'
      };
    }
  }

  /**
   * 获取可用模型列表
   */
  async getAvailableModels(config: AIProviderConfig): Promise<string[]> {
    console.log('🔍 [OpenAIProvider.getAvailableModels] 开始获取模型列表:', {
      baseUrl: config.baseUrl,
      endsWithHash: config.baseUrl.endsWith('#')
    });

    const normalizedBaseUrl = this.normalizeBaseUrl(config.baseUrl);
    console.log('🔧 [OpenAIProvider.getAvailableModels] 标准化后的URL:', normalizedBaseUrl);

    // {{ AURA-X: Modify - 修复#结尾URL的模型获取路径. Approval: 寸止(ID:**********). }}
    const apiUrl = config.baseUrl.endsWith('#')
      ? `${normalizedBaseUrl}/models`
      : `${normalizedBaseUrl}/v1/models`;

    console.log('🚀 [OpenAIProvider.getAvailableModels] 最终API URL:', apiUrl);

    const response = await fetch(apiUrl, {
      headers: {
        'Authorization': `Bearer ${config.apiKey}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`获取OpenAI模型列表失败: ${response.status}`);
    }

    const result = await response.json();
    return result.data.map((model: any) => model.id);
  }

  /**
   * 将文件转换为base64
   */
  private async fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        resolve(result);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  /**
   * 发送图片识别请求
   */
  private async sendImageRequest(base64Images: string[], additionalContext?: string, userContext?: any): Promise<any> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      // {{ AURA-X: Modify - 使用迁移的增强版提示词. Approval: 寸止(ID:1737098100). }}
      // 根据图片数量选择合适的提示词
      const isMultiImage = base64Images.length > 1;
      const promptId = isMultiImage ? 'multi_image_food_recognition' : 'single_image_food_recognition';
      const prompt = promptManager.renderPrompt(promptId, {
        imageCount: base64Images.length,
        additionalContext: additionalContext || '',
        userContext: userContext || null
      });

      // 构建消息内容
      const content: any[] = [
        { type: 'text', text: prompt }
      ];

      // 添加图片
      base64Images.forEach(base64 => {
        content.push({
          type: 'image_url',
          image_url: {
            url: base64,
            detail: 'high'
          }
        });
      });

      const apiUrl = this.buildApiUrl('/chat/completions');
      const response = await fetch(apiUrl, {
        method: 'POST',
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify({
          model: this.modelName,
          messages: [
            {
              role: 'user',
              content: content
            }
          ],
          // {{ AURA-X: Modify - 统一AI参数配置，参照geminiService.ts最佳实践. Approval: 寸止(ID:1737098200). }}
          temperature: 0.05,  // 降低温度以提高JSON格式稳定性
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API请求失败: ${response.status} ${response.statusText} - ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('请求超时，请重试');
      }
      throw error;
    } finally {
      clearTimeout(timeoutId);
    }
  }

  /**
   * 发送文本分析请求
   */
  private async sendTextRequest(text: string): Promise<any> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const prompt = promptManager.renderPrompt('text_food_analysis', { text });

      const apiUrl = this.buildApiUrl('/chat/completions');
      const response = await fetch(apiUrl, {
        method: 'POST',
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify({
          model: this.modelName,
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.05
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API请求失败: ${response.status} ${response.statusText} - ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('请求超时，请重试');
      }
      throw error;
    } finally {
      clearTimeout(timeoutId);
    }
  }

  /**
   * 发送营养建议请求
   */
  private async sendAdviceRequest(prompt: string): Promise<any> {
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.timeout);

    try {
      const apiUrl = this.buildApiUrl('/chat/completions');
      const response = await fetch(apiUrl, {
        method: 'POST',
        signal: controller.signal,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify({
          model: this.modelName,
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.7
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`API请求失败: ${response.status} ${response.statusText} - ${errorText}`);
      }

      return await response.json();
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        throw new Error('请求超时，请重试');
      }
      throw error;
    } finally {
      clearTimeout(timeoutId);
    }
  }

  /**
   * 解析图片识别响应
   */
  private parseImageResponse(response: any): AIImageRecognitionResult {
    try {
      if (!response.choices || !response.choices[0] || !response.choices[0].message) {
        throw new Error('API响应格式不正确');
      }

      const content = response.choices[0].message.content;
      const cleanedContent = content.replace(/```json\s*|\s*```/g, '').trim();

      const result = JSON.parse(cleanedContent);

      // 验证必要字段
      if (!result.foods || !Array.isArray(result.foods)) {
        throw new Error('响应中缺少foods字段或格式不正确');
      }

      return result as AIImageRecognitionResult;
    } catch (error) {
      console.error('解析OpenAI图片识别响应失败:', error);
      throw new Error('解析AI响应失败，请重试');
    }
  }

  /**
   * 解析文本分析响应
   */
  private parseTextResponse(response: any): AITextAnalysisResult {
    try {
      if (!response.choices || !response.choices[0] || !response.choices[0].message) {
        throw new Error('API响应格式不正确');
      }

      const content = response.choices[0].message.content;
      const cleanedContent = content.replace(/```json\s*|\s*```/g, '').trim();

      const result = JSON.parse(cleanedContent);

      // 验证必要字段
      if (!result.foods || !Array.isArray(result.foods)) {
        throw new Error('响应中缺少foods字段或格式不正确');
      }

      return result as AITextAnalysisResult;
    } catch (error) {
      console.error('解析OpenAI文本分析响应失败:', error);
      throw new Error('解析AI响应失败，请重试');
    }
  }

  /**
   * 解析营养建议响应
   */
  private parseAdviceResponse(response: any): { advice: string; rawResponse?: any } {
    try {
      if (!response.choices || !response.choices[0] || !response.choices[0].message) {
        throw new Error('API响应格式不正确');
      }

      const advice = response.choices[0].message.content;

      return {
        advice: advice.trim(),
        rawResponse: response
      };
    } catch (error) {
      console.error('解析OpenAI营养建议响应失败:', error);
      throw new Error('解析AI响应失败，请重试');
    }
  }
}

// {{ AURA-X: Add - 添加OpenAI响应解析方法. Approval: 寸止(ID:1736938100). }}
