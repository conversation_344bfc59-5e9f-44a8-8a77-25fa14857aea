import React, { useState, useRef } from 'react';
import { getCurrentMealType } from '@/shared/utils/snackTimeUtils';
import { useFoodRecognition, FoodRecognitionResult } from '@/shared/hooks/useFoodRecognition';
import { CalorieDisplay, WeightDisplay } from '@/shared/components/atoms';
import { getExerciseSuggestions, formatExerciseTime } from '@/shared/utils/exerciseCalories';
import { useUserStore } from '@/domains/user/stores/userStore';
import { formatDate } from '@/shared/utils';

export type RecognitionMethod = 'text' | 'image';
export type MealType = 'breakfast' | 'lunch' | 'dinner' | 'morning-snack' | 'afternoon-snack' | 'evening-snack';

interface FoodRecognitionModalProps {
  isOpen: boolean;
  onClose: () => void;
  method: RecognitionMethod;
  onRecognitionComplete?: (result: any) => void;
  currentDate?: Date;
  onProcessingStateChange?: (isProcessing: boolean) => void; // 添加处理状态回调
}

const FoodRecognitionModal: React.FC<FoodRecognitionModalProps> = ({
  isOpen,
  onClose,
  method,
  onRecognitionComplete,
  currentDate,
  onProcessingStateChange
}) => {
  // 添加手动餐次选择功能
  const [textInput, setTextInput] = useState('');
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);
  const [selectedMealType, setSelectedMealType] = useState<MealType>(getCurrentMealType());
  const [recognitionResult, setRecognitionResult] = useState<FoodRecognitionResult | null>(null);
  const [additionalContext, setAdditionalContext] = useState('');
  const [showResultsView, setShowResultsView] = useState(false);
  const [isMealSelectorExpanded, setIsMealSelectorExpanded] = useState(false);
  const [enlargedImageIndex, setEnlargedImageIndex] = useState<number | null>(null);
  const [selectedDate, setSelectedDate] = useState<Date>(currentDate || new Date());
  const fileInputRef = useRef<HTMLInputElement>(null);
  const cameraInputRef = useRef<HTMLInputElement>(null);
  const { state, startRecognition, stopRecognition } = useFoodRecognition();
  const { profile } = useUserStore();

  // 餐次配置
  const mealOptions = [
    { value: 'breakfast', label: '早餐', icon: '🌅' },
    { value: 'lunch', label: '午餐', icon: '☀️' },
    { value: 'dinner', label: '晚餐', icon: '🌙' },
    { value: 'morning-snack', label: '上午加餐', icon: '🍎' },
    { value: 'afternoon-snack', label: '下午加餐', icon: '🥨' },
    { value: 'evening-snack', label: '晚间加餐', icon: '🥛' }
  ];

  // 获取当前选中餐次的配置
  const getCurrentMealOption = () => {
    return mealOptions.find(meal => meal.value === selectedMealType) || mealOptions[0];
  };

  // 处理餐次选择
  const handleMealSelect = (mealType: MealType) => {
    setSelectedMealType(mealType);
    setIsMealSelectorExpanded(false);
  };

  // 获取当前时间对应的餐次（使用工具函数）
  const getDefaultMealType = (): MealType => {
    return getCurrentMealType();
  };

  // 获取餐次对应的图标
  const getMealIcon = (mealType: MealType): string => {
    const mealConfig = {
      breakfast: '🌅',
      lunch: '☀️',
      dinner: '🌙',
      'morning-snack': '🍎',
      'afternoon-snack': '🥨',
      'evening-snack': '🥛'
    };
    return mealConfig[mealType];
  };

  // 重置状态（完全清理所有状态）
  const resetState = () => {
    setTextInput('');
    setSelectedImages([]);
    setImagePreviews([]);
    setRecognitionResult(null);
    setAdditionalContext('');
    setShowResultsView(false);
    setIsMealSelectorExpanded(false);
    setEnlargedImageIndex(null);
    // 设置默认餐次为当前时间推荐的餐次
    setSelectedMealType(getCurrentMealType());

    // 清理文件输入
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
    if (cameraInputRef.current) {
      cameraInputRef.current.value = '';
    }

    // 停止任何正在进行的识别
    stopRecognition();
  };

  // 当模态框打开时设置默认餐次
  React.useEffect(() => {
    if (isOpen) {
      setSelectedMealType(getCurrentMealType());
    }
  }, [isOpen]);

  // 处理弹窗关闭（确保完全清理状态）
  const handleClose = () => {
    if (!state.isProcessing) {
      resetState();
      // 通知父组件AI处理结束
      onProcessingStateChange?.(false);
      // 确保所有异步操作都被清理
      setTimeout(() => {
        onClose();
      }, 0);
    }
  };

  // 处理图片选择（支持多张）
  const handleImageSelect = (file: File) => {
    if (selectedImages.length >= 5) {
      alert('最多只能上传5张图片');
      return;
    }

    // 类型检查确保file是有效的File对象
    if (!file || !(file instanceof File)) {
      console.error('Invalid file object:', file);
      return;
    }

    const newImages = [...selectedImages, file];
    setSelectedImages(newImages);

    const reader = new FileReader();
    reader.onload = (e) => {
      const result = e.target?.result;
      if (typeof result === 'string') {
        const newPreviews = [...imagePreviews, result];
        setImagePreviews(newPreviews);
      }
    };
    reader.onerror = (e) => {
      console.error('FileReader error:', e);
      alert('图片读取失败，请重试');
    };
    reader.readAsDataURL(file);
  };

  // 删除单张图片
  const handleRemoveImage = (index: number) => {
    const newImages = selectedImages.filter((_, i) => i !== index);
    const newPreviews = imagePreviews.filter((_, i) => i !== index);
    setSelectedImages(newImages);
    setImagePreviews(newPreviews);
  };

  // 处理相机拍照 - 使用HTML5 input
  const handleCameraCapture = () => {
    cameraInputRef.current?.click();
  };

  // 处理相机拍照完成
  const handleCameraPhotoCapture = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file || !(file instanceof File)) {
      console.error('Invalid camera file:', file);
      return;
    }

    handleImageSelect(file);

    // 重要：清空input的value，确保下次拍照能触发onChange事件
    // 这样每次拍照都会捕获新的图像，而不是缓存的图像
    if (event.target) {
      event.target.value = '';
    }
  };

  // 处理从相册选择文件
  const handleFileUpload = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // 开始识别 - 集成AURA-X协议
  const handleStartRecognition = async () => {
    // 通知父组件AI处理开始
    onProcessingStateChange?.(true);

    try {
      // AURA-X协议：确保所有决策通过用户确认
      // 使用用户手动选择的餐次，如果没有选择则使用自动判断
      const finalMealType = selectedMealType || getCurrentMealType();

      // 对于图片识别，将额外上下文与文本输入合并
      const combinedTextInput = method === 'image' && additionalContext.trim()
        ? `${additionalContext.trim()}`
        : textInput;

      // 支持多张图片识别 - 目前只使用第一张图片
      const imagesToUse = selectedImages.length > 0 ? selectedImages[0] : null;

      // 准备用户个人数据用于AI个性化建议
      const userContext = profile ? {
        age: profile.age,
        gender: profile.gender,
        weight: profile.weight,
        height: profile.height,
        targetWeight: profile.targetWeight,
        activityLevel: profile.activityLevel,
        bmr: profile.bmr,
        tdee: profile.tdee,
        targetDays: profile.targetDays
      } : null;

      await startRecognition(
        method,
        finalMealType,
        combinedTextInput,
        imagesToUse,
        (result) => {
          setRecognitionResult(result);
          setShowResultsView(true);
          // AI处理完成，通知父组件
          onProcessingStateChange?.(false);
        }
      );
    } catch (error) {
      console.error('识别失败:', error);
      // 出错时也要通知处理结束
      onProcessingStateChange?.(false);
    }
  };

  // 处理最终提交
  const handleFinalSubmit = () => {
    console.log('handleFinalSubmit 被调用', { recognitionResult, selectedMealType, selectedDate });

    // {{ AURA-X: Modify - 增强确认添加按钮的健壮性和错误处理. Approval: 寸止(ID:1736937200). }}
    if (!recognitionResult) {
      console.error('recognitionResult 为空，无法提交');
      alert('识别结果丢失，请重新识别');
      return;
    }

    if (!recognitionResult.foods || recognitionResult.foods.length === 0) {
      console.error('识别结果中没有食物数据');
      alert('没有识别到食物，请重新识别');
      return;
    }

    try {
      // 使用用户选择的餐次和日期覆盖AI识别的餐次
      const finalResult = {
        ...recognitionResult,
        meal: selectedMealType, // 使用手动选择的餐次
        selectedDate: selectedDate, // 添加选择的日期
        additionalContext: additionalContext.trim() || undefined
      };

      console.log('准备调用 onRecognitionComplete', finalResult);

      if (typeof onRecognitionComplete === 'function') {
        // 先恢复状态，再保存和关闭
        onProcessingStateChange?.(false);
        onRecognitionComplete(finalResult);
        // 延迟关闭，确保状态恢复生效
        setTimeout(() => {
          handleClose();
        }, 100);
      } else {
        console.error('onRecognitionComplete 不是一个函数');
        // 出错时也要恢复状态
        onProcessingStateChange?.(false);
        alert('提交失败，请重试');
      }
    } catch (error) {
      console.error('提交过程中发生错误:', error);
      // 出错时恢复状态
      onProcessingStateChange?.(false);
      alert('提交失败，请重试');
    }
  };

  // 重新识别（保留补充描述）
  const handleReRecognize = () => {
    setRecognitionResult(null);
    setShowResultsView(false);
    // 保留additionalContext，不清空用户输入的补充描述
  };

  // 获取准确度颜色
  const getConfidenceColor = (confidence: number): string => {
    if (confidence >= 0.8) return 'bg-green-100 text-green-800';
    if (confidence >= 0.6) return 'bg-yellow-100 text-yellow-800';
    return 'bg-red-100 text-red-800';
  };

  // 获取准确度文本
  const getConfidenceText = (confidence: number): string => {
    if (confidence >= 0.8) return '高准确度';
    if (confidence >= 0.6) return '中等准确度';
    return '低准确度';
  };

  // 终止识别
  const handleStopRecognition = () => {
    stopRecognition();
    // 通知父组件AI处理结束
    onProcessingStateChange?.(false);
  };

  // 检查是否可以开始识别
  const canStartRecognition = () => {
    if (method === 'text') {
      return textInput.trim().length > 0;
    }
    return selectedImages.length > 0;
  };

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 flex items-center justify-center z-50 p-4 pb-20"
      style={{
        backgroundColor: 'rgba(0, 0, 0, 0.4)',
        backdropFilter: 'blur(8px)',
        // {{ AURA-X: Modify - 修复PWA模式下的闪烁问题. Approval: 寸止(ID:1737098300). }}
        willChange: 'auto', // 避免不必要的GPU层创建
        transform: 'translateZ(0)', // 强制GPU加速但避免层叠上下文问题
        ...(state.isProcessing && { touchAction: 'none' })
      }}
      onClick={handleClose}
      onTouchMove={state.isProcessing ? (e) => e.preventDefault() : undefined}
    >
      <div
        className="relative bg-white rounded-2xl w-full max-w-sm sm:max-w-md max-h-[80vh] sm:max-h-[75vh] overflow-hidden shadow-2xl border border-gray-100 flex flex-col"
        style={{
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.05)',
          // {{ AURA-X: Modify - 优化PWA模式下的渲染性能. Approval: 寸止(ID:1737098300). }}
          backfaceVisibility: 'hidden',
          perspective: '1000px'
        }}
        onClick={(e) => e.stopPropagation()}
      >
        {/* 弹窗头部 */}
        <div className="flex items-center justify-between p-6 pb-4">
          <div>
            <h2 className="text-xl font-bold text-gray-900 mb-1">
              {method === 'text' ? '文本识别' : '视觉识别'}
            </h2>
            <p className="text-sm text-gray-500">
              {method === 'text' ? '通过对话描述识别食物' : '通过拍照识别食物'}
            </p>
          </div>
          <button
            onClick={handleClose}
            className="btn btn-error text-white btn-sm xl:btn-md btn-circle hover:bg-red-600 transition-colors focus:outline-none focus:ring-2 focus:ring-red-500"
            disabled={state.isProcessing}
            style={state.isProcessing ? { opacity: 0.5, pointerEvents: 'none' } : {}}
            tabIndex={0}
          >
            <svg className="w-4 h-4 xl:w-5 xl:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* 弹窗内容 */}
        <div className="flex-1 overflow-y-auto p-6 pt-2 space-y-6 pb-16">
          {!showResultsView ? (
            <>
              {/* 折叠式餐次选择器 */}
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-3">选择餐次</h3>

                {/* 折叠状态 - 显示当前选中餐次 */}
                {!isMealSelectorExpanded && (
                  <button
                    onClick={() => setIsMealSelectorExpanded(true)}
                    className="w-full p-3 rounded-xl border-2 border-emerald-500 bg-emerald-50 text-emerald-700 transition-all duration-200 hover:bg-emerald-100"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <span className="text-lg flex-shrink-0">{getCurrentMealOption().icon}</span>
                        <span className="font-medium">{getCurrentMealOption().label}</span>
                      </div>
                      <svg className="w-5 h-5 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </button>
                )}

                {/* 展开状态 - 显示所有餐次选项 */}
                {isMealSelectorExpanded && (
                  <div className="space-y-3">
                    <div className="grid grid-cols-2 gap-3">
                      {mealOptions.map((meal) => (
                        <button
                          key={meal.value}
                          onClick={() => handleMealSelect(meal.value as MealType)}
                          className={`p-3 rounded-xl border-2 transition-all duration-200 whitespace-nowrap ${
                            selectedMealType === meal.value
                              ? 'border-emerald-500 bg-emerald-50 text-emerald-700'
                              : 'border-gray-200 bg-white text-gray-700 hover:border-emerald-300 hover:bg-emerald-50'
                          }`}
                        >
                          <div className="flex flex-col items-center gap-1">
                            <span className="text-lg flex-shrink-0">{meal.icon}</span>
                            <span className="text-sm font-medium">{meal.label}</span>
                          </div>
                        </button>
                      ))}
                    </div>
                    <button
                      onClick={() => setIsMealSelectorExpanded(false)}
                      className="w-full text-center text-sm text-gray-500 hover:text-gray-700 transition-colors duration-200"
                    >
                      收起选项 ↑
                    </button>
                  </div>
                )}
              </div>

              {/* {{ AURA-X: Add - 在识别开始前添加日期选择器. Approval: 寸止(ID:1736937200). }} */}
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-3">选择记录日期</h3>
                <div className="bg-blue-50 rounded-xl p-4 border border-blue-100">
                  <div className="flex items-center justify-between mb-2">
                  </div>
                  <input
                    type="date"
                    value={selectedDate.toISOString().split('T')[0]}
                    onChange={(e) => setSelectedDate(new Date(e.target.value))}
                    max={new Date().toISOString().split('T')[0]}
                    className="input input-bordered input-sm w-full bg-white"
                  />
                  <div className="text-xs text-gray-500 mt-1">
                    当前选择：{selectedDate.toLocaleDateString('zh-CN', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      weekday: 'long'
                    })}
                  </div>
                </div>
              </div>
            </>
          ) : (
            /* 识别结果视图 */
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold text-gray-800">识别结果</h3>
              </div>

              {recognitionResult && (
                <div className="space-y-3">
                  {recognitionResult.foods.map((food, index) => (
                    <div key={index} className="border border-gray-200 rounded-lg p-4">
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900">{food.name}</h4>
                          {food.dataSource === 'nutrition_label' && food.labelInfo?.hasLabel && (
                            <div className="flex items-center gap-1 mt-1">
                              <span className="text-xs bg-green-100 text-green-800 px-2 py-0.5 rounded-full">
                                📋 营养标签数据
                              </span>
                              {food.labelInfo.servingSize && (
                                <span className="text-xs text-gray-500">
                                  份量: {food.labelInfo.servingSize}
                                </span>
                              )}
                            </div>
                          )}
                          {food.dataSource === 'visual_estimation' && (
                            <span className="text-xs bg-blue-100 text-blue-800 px-2 py-0.5 rounded-full mt-1 inline-block">
                              👁️ 视觉估算
                            </span>
                          )}
                          {food.dataSource === 'nutrition_label' && (
                            <span className="text-xs bg-purple-100 text-purple-800 px-2 py-0.5 rounded-full mt-1 inline-block">
                              📝 营养标签
                            </span>
                          )}
                        </div>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getConfidenceColor(food.confidence)}`}>
                          {getConfidenceText(food.confidence)}
                        </span>
                      </div>
                      <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                        <div className="whitespace-nowrap">
                          <span className="font-medium">卡路里：</span>
                          <CalorieDisplay value={food.calories} />
                        </div>
                        <div className="whitespace-nowrap">
                          <span className="font-medium">重量：</span>
                          <WeightDisplay value={food.weight} />
                        </div>
                      </div>
                    </div>
                  ))}

                  {/* 运动消耗建议模块 */}
                  {(() => {
                    const totalCalories = recognitionResult.foods.reduce((sum, food) => sum + food.calories, 0);
                    const exerciseSuggestions = getExerciseSuggestions(totalCalories);

                    // 使用AI返回的个性化建议，如果没有则使用默认建议
                    const adviceText = recognitionResult.personalizedAdvice ||
                      `根据您的个人数据分析，这份食物含有 ${totalCalories} 卡路里。`;

                    return (
                      <div className="bg-gradient-to-r from-orange-50 to-red-50 rounded-lg p-4 border border-orange-200">
                        <div className="flex items-center gap-2 mb-3">
                          <span className="text-lg">🔥</span>
                          <h5 className="font-medium text-gray-900">个性化运动建议</h5>
                        </div>

                        <p className="text-sm text-gray-600 mb-3">{adviceText}</p>

                        <div className="text-sm text-gray-700 mb-2">
                          <span className="font-medium">消耗 {totalCalories} kcal 需要：</span>
                        </div>

                        <div className="grid grid-cols-2 gap-2 text-xs">
                          {exerciseSuggestions.slice(0, 4).map((suggestion) => (
                            <div key={suggestion.type} className="flex items-center gap-2 bg-white rounded-lg p-2">
                              <span>{suggestion.icon}</span>
                              <span>{suggestion.name} {formatExerciseTime(suggestion.minutes)}</span>
                            </div>
                          ))}
                        </div>

                        {/* 显示AI个性化运动建议 */}
                        {recognitionResult.exerciseAdvice && (
                          <div className="mt-3 p-2 bg-white rounded-lg">
                            <div className="text-xs text-gray-700">
                              <span className="font-medium">💪 专属建议：</span>
                              {recognitionResult.exerciseAdvice}
                            </div>
                          </div>
                        )}

                        <div className="mt-3 text-xs text-gray-500 text-center">
                          💡 基于您的个人数据提供专属建议
                        </div>
                      </div>
                    );
                  })()}
                </div>
              )}

              {/* 在结果视图中添加餐次选择和额外上下文输入 */}
              {showResultsView && (
                <div className="space-y-4">
                  {/* 折叠式餐次选择器 */}
                  <div>
                    <h4 className="text-base font-medium text-gray-800 mb-3">确认餐次</h4>

                    {/* 折叠状态 - 显示当前选中餐次 */}
                    {!isMealSelectorExpanded && (
                      <button
                        onClick={() => setIsMealSelectorExpanded(true)}
                        className="w-full p-3 rounded-xl border-2 border-emerald-500 bg-emerald-50 text-emerald-700 transition-all duration-200 hover:bg-emerald-100"
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-3">
                            <span className="text-lg flex-shrink-0">{getCurrentMealOption().icon}</span>
                            <span className="font-medium">{getCurrentMealOption().label}</span>
                          </div>
                          <svg className="w-5 h-5 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                          </svg>
                        </div>
                      </button>
                    )}

                    {/* 展开状态 - 显示所有餐次选项 */}
                    {isMealSelectorExpanded && (
                      <div className="space-y-3">
                        <div className="grid grid-cols-3 gap-2">
                          {mealOptions.map((meal) => (
                            <button
                              key={meal.value}
                              onClick={() => handleMealSelect(meal.value as MealType)}
                              className={`p-2 rounded-lg border-2 transition-all duration-200 whitespace-nowrap ${
                                selectedMealType === meal.value
                                  ? 'border-emerald-500 bg-emerald-50 text-emerald-700'
                                  : 'border-gray-200 bg-white text-gray-700 hover:border-emerald-300 hover:bg-emerald-50'
                              }`}
                            >
                              <div className="flex flex-col items-center gap-1">
                                <span className="text-sm flex-shrink-0">{meal.icon}</span>
                                <span className="text-xs font-medium">{meal.label}</span>
                              </div>
                            </button>
                          ))}
                        </div>
                        <button
                          onClick={() => setIsMealSelectorExpanded(false)}
                          className="w-full text-center text-sm text-gray-500 hover:text-gray-700 transition-colors duration-200"
                        >
                          收起选项 ↑
                        </button>
                      </div>
                    )}
                  </div>

                  {/* {{ AURA-X: Remove - 移除结果视图中的日期选择器，已移动到识别开始前. Approval: 寸止(ID:1736937200). }} */}

                  {/* 补充信息 */}
                  <div>
                    <h4 className="text-base font-medium text-gray-800">补充信息（可选）</h4>
                    <textarea
                      value={additionalContext}
                      onChange={(e) => setAdditionalContext(e.target.value)}
                      placeholder="如果识别结果需要修正或补充，请在此描述..."
                      className="textarea textarea-bordered w-full h-24 resize-none text-sm"
                      disabled={state.isProcessing}
                    />
                    <div className="text-xs text-gray-500">
                      💡 例如：重量应该是150g，或者这是低脂版本
                    </div>
                  </div>
                </div>
              )}


            </div>
          )}

          {/* 内容输入区域 - 只在非结果视图时显示 */}
          {!showResultsView && (
            <>
              {method === 'text' ? (
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-3">描述食物</h3>
                  <textarea
                    value={textInput}
                    onChange={(e) => setTextInput(e.target.value)}
                    placeholder="例如：一碗白米饭，一个苹果，200ml牛奶..."
                    className="textarea textarea-bordered w-full h-32 resize-none"
                    disabled={state.isProcessing}
                    style={state.isProcessing ? { opacity: 0.5, pointerEvents: 'none' } : {}}
                  />
                  <div className="text-xs text-slate-500 mt-2">
                    💡 提示：描述越详细，AI识别越准确
                  </div>
                </div>
              ) : (
                <div className="space-y-4">
                  <div>
                    <h3 className="text-base font-medium text-slate-800 mb-2">
                      选择图片 ({selectedImages.length}/5)
                    </h3>

                    {/* 图片预览网格 */}
                    {imagePreviews.length > 0 && (
                      <div className="grid grid-cols-3 gap-2 mb-3">
                        {imagePreviews.map((preview, index) => (
                          <div key={index} className="relative">
                            <img
                              src={preview}
                              alt={`预览 ${index + 1}`}
                              className="w-full h-20 object-cover rounded-lg border border-slate-200 cursor-pointer hover:opacity-80 transition-opacity"
                              onClick={() => setEnlargedImageIndex(index)}
                            />
                            <button
                              onClick={(e) => {
                                e.stopPropagation();
                                handleRemoveImage(index);
                              }}
                              className="absolute -top-1 -right-1 btn btn-ghost btn-xs btn-circle bg-red-500 text-white hover:bg-red-600"
                              disabled={state.isProcessing}
                            >
                              ✕
                            </button>
                          </div>
                        ))}
                      </div>
                    )}

                    {/* 添加图片按钮 */}
                    {selectedImages.length < 5 && (
                      <div className="grid grid-cols-2 gap-3">
                        <button
                          onClick={handleCameraCapture}
                          className="btn btn-outline flex flex-col items-center gap-2 h-20 whitespace-nowrap"
                          disabled={state.isProcessing}
                          style={state.isProcessing ? { opacity: 0.5, pointerEvents: 'none' } : {}}
                        >
                          <span className="text-2xl flex-shrink-0">📷</span>
                          <span className="text-sm">拍照识别</span>
                        </button>
                        <button
                          onClick={handleFileUpload}
                          className="btn btn-outline flex flex-col items-center gap-2 h-20 whitespace-nowrap"
                          disabled={state.isProcessing}
                          style={state.isProcessing ? { opacity: 0.5, pointerEvents: 'none' } : {}}
                        >
                          <span className="text-2xl flex-shrink-0">📁</span>
                          <span className="text-sm">从相册选择</span>
                        </button>
                      </div>
                    )}
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      onChange={(e) => {
                        const file = e.target.files?.[0];
                        if (file && file instanceof File) {
                          handleImageSelect(file);
                        } else if (file) {
                          console.error('Invalid file type from input:', file);
                        }
                      }}
                      className="hidden"
                    />
                  </div>

                  {/* 图片识别时的额外上下文输入 */}
                  <div className="space-y-3">
                    <h4 className="text-base font-medium text-gray-800">补充描述（可选）</h4>
                    <textarea
                      value={additionalContext}
                      onChange={(e) => setAdditionalContext(e.target.value)}
                      placeholder="提供额外信息帮助AI更准确识别，如：这是一个中等大小的苹果，大约150g..."
                      className="textarea textarea-bordered w-full h-20 resize-none text-sm"
                      disabled={state.isProcessing}
                    />
                    <div className="text-xs text-gray-500">
                      💡 例如：食物大小、品牌、特殊制作方式等
                    </div>
                  </div>
                </div>
              )}
            </>
          )}

          {/* 错误提示 */}
          {state.error && (
            <div className="alert alert-error">
              <span className="text-white">{state.error}</span>
            </div>
          )}

          {/* 进度提示 */}
          {state.isProcessing && state.processingStep && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
              <div className="flex items-center gap-2">
                <div className="loading loading-spinner loading-sm text-blue-600"></div>
                <span className="text-sm text-blue-700 font-medium">{state.processingStep}</span>
              </div>
            </div>
          )}

          {/* 底部间距保障 */}
          <div className="h-4"></div>
        </div>

        {/* 操作按钮 */}
        <div className="absolute bottom-0 left-0 right-0 flex gap-3 p-4 pt-3 bg-gray-50 rounded-b-2xl border-t border-gray-100">
          {!state.isProcessing ? (
            showResultsView ? (
              /* 结果视图按钮 */
              <>
                <button
                  onClick={handleReRecognize}
                  className="btn btn-outline flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px] whitespace-nowrap"
                >
                  重新识别
                </button>
                <button
                  onClick={handleFinalSubmit}
                  className="btn btn-primary text-white flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px] whitespace-nowrap"
                >
                  <svg className="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  <span>确认添加</span>
                </button>
              </>
            ) : (
              /* 初始识别按钮 */
              <button
                onClick={handleStartRecognition}
                disabled={!canStartRecognition() || state.isProcessing}
                className="btn btn-primary text-white flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px] whitespace-nowrap"
                style={state.isProcessing ? { opacity: 0.5, pointerEvents: 'none' } : {}}
              >
                <svg className="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
                <span>开始识别</span>
              </button>
            )
          ) : (
            <button
              onClick={handleStopRecognition}
              className="btn btn-error text-white flex-1 rounded-xl shadow-sm hover:shadow-md transition-all duration-200 min-h-[44px] whitespace-nowrap"
            >
              <svg className="w-4 h-4 mr-1 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
              <span>终止识别</span>
            </button>
          )}
        </div>

        {/* 隐藏的相机输入 - 使用HTML5 capture */}
        <input
          ref={cameraInputRef}
          type="file"
          accept="image/*"
          capture="environment"
          onChange={handleCameraPhotoCapture}
          className="hidden"
        />

        {/* 图片放大查看模态框 */}
        {enlargedImageIndex !== null && (
          <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 pb-20" onClick={() => setEnlargedImageIndex(null)}>
            <div className="relative max-w-4xl max-h-4xl p-4">
              <img
                src={imagePreviews[enlargedImageIndex]}
                alt={`放大预览 ${enlargedImageIndex + 1}`}
                className="max-w-full max-h-full object-contain rounded-lg"
                onClick={(e) => e.stopPropagation()}
              />

              {/* 关闭按钮 */}
              <button
                onClick={() => setEnlargedImageIndex(null)}
                className="absolute top-2 right-2 btn btn-ghost btn-circle bg-white text-black hover:bg-gray-200"
              >
                ✕
              </button>

              {/* 图片导航 */}
              {imagePreviews.length > 1 && (
                <>
                  {enlargedImageIndex > 0 && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setEnlargedImageIndex(enlargedImageIndex - 1);
                      }}
                      className="absolute left-2 top-1/2 transform -translate-y-1/2 btn btn-ghost btn-circle bg-white text-black hover:bg-gray-200"
                    >
                      ←
                    </button>
                  )}
                  {enlargedImageIndex < imagePreviews.length - 1 && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setEnlargedImageIndex(enlargedImageIndex + 1);
                      }}
                      className="absolute right-2 top-1/2 transform -translate-y-1/2 btn btn-ghost btn-circle bg-white text-black hover:bg-gray-200"
                    >
                      →
                    </button>
                  )}
                </>
              )}

              {/* 图片计数 */}
              <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-50 text-white px-3 py-1 rounded-full text-sm">
                {enlargedImageIndex + 1} / {imagePreviews.length}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default FoodRecognitionModal;
