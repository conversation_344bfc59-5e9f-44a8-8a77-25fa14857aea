/**
 * AI模型管理相关类型定义
 */

export type AIModelProvider = 'openai' | 'gemini';
// {{ AURA-X: Modify - 移除夏目提供商类型. Approval: 寸止(ID:**********). }}

export interface AIModelConfig {
  id: string;
  name: string;
  provider: AIModelProvider;
  apiKey: string;
  baseUrl: string;
  modelName: string;
  supportsVision: boolean | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface AIModelCapabilities {
  supportsText: boolean;
  supportsVision: boolean;
  supportsAudio: boolean;
  maxTokens?: number;
  supportedFormats?: string[];
}

export interface AIModelValidationResult {
  isValid: boolean;
  capabilities?: AIModelCapabilities;
  error?: string;
  modelList?: string[];
}

export interface OpenAIModelInfo {
  id: string;
  object: string;
  created: number;
  owned_by: string;
}

export interface GeminiModelInfo {
  name: string;
  displayName: string;
  description: string;
  inputTokenLimit: number;
  outputTokenLimit: number;
  supportedGenerationMethods: string[];
}

// {{ AURA-X: Add - 扩展模型数据结构支持缓存和验证状态. Approval: 寸止(ID:**********). }}
export interface CachedModelData {
  models: Array<{id: string, supportsVision: boolean | null}>;
  timestamp: number;
  provider: AIModelProvider;
  apiKey: string;
  baseUrl: string;
}

export interface ValidationState {
  isValidated: boolean;
  validationTimestamp: number;
  validationResult: string;
  configHash: string; // 用于检测配置变更
}

export interface AIModelStore {
  models: AIModelConfig[];
  activeModelId: string | null;
  // {{ AURA-X: Add - 添加模型列表缓存和验证状态. Approval: 寸止(ID:**********). }}
  cachedModelData: Record<string, CachedModelData>; // key: provider-apiKey-baseUrl的hash
  validationStates: Record<string, ValidationState>; // key: 配置的hash
  addModel: (config: Omit<AIModelConfig, 'id' | 'createdAt' | 'updatedAt'>) => void;
  updateModel: (id: string, updates: Partial<AIModelConfig>) => void;
  deleteModel: (id: string) => void;
  setActiveModel: (id: string) => void;
  getActiveModel: () => AIModelConfig | null;
  fixModelVisionSupport: () => void;
  validateModel: (config: Partial<AIModelConfig>) => Promise<AIModelValidationResult>;
  getModelList: (provider: AIModelProvider, apiKey: string, baseUrl: string) => Promise<string[]>;
  // {{ AURA-X: Add - 新增缓存和验证状态管理方法. Approval: 寸止(ID:**********). }}
  getCachedModels: (config: Pick<AIModelConfig, 'provider' | 'apiKey' | 'baseUrl'>) => Array<{id: string, supportsVision: boolean | null}> | null;
  setCachedModels: (config: Pick<AIModelConfig, 'provider' | 'apiKey' | 'baseUrl'>, models: Array<{id: string, supportsVision: boolean | null}>) => void;
  getValidationState: (config: Pick<AIModelConfig, 'provider' | 'apiKey' | 'baseUrl' | 'modelName'>) => ValidationState | null;
  setValidationState: (config: Pick<AIModelConfig, 'provider' | 'apiKey' | 'baseUrl' | 'modelName'>, state: Omit<ValidationState, 'configHash'>) => void;
  clearValidationState: (config: Pick<AIModelConfig, 'provider' | 'apiKey' | 'baseUrl' | 'modelName'>) => void;
  isConfigurationChanged: (config: Pick<AIModelConfig, 'provider' | 'apiKey' | 'baseUrl' | 'modelName'>) => boolean;
}
